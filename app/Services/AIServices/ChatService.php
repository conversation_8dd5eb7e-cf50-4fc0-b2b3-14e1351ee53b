<?php

namespace App\Services\AIServices;

use App\Services\AIServices\AIServiceFactory;
use Illuminate\Support\Facades\Log;

class ChatService
{
    private $analyzer = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 500,
        'temperature' => 0.3,
    ];

    private $responder = [
        'provider' => 'deepseek',
        'model' => 'deepseek-chat',
        'max_tokens' => 300,
        'temperature' => 0.7,
    ];

    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {

            $analysis = $this->analyzeRequest($userMessage, $context);

            // 3. إنشاء الرد النهائي (مع السياق - هنا نحتاج المحادثات السابقة)
            $response = $this->generateFinalResponse($userMessage, $analysis, $context);

            return $response;
        } catch (\Exception $e) {
            Log::error('Chat Service Error: ' . $e->getMessage(), [
                'user_message' => $userMessage,
                'context' => $context
            ]);
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * 1. تحليل الطلب
     */
   private function analyzeRequest(string $userMessage, array $context): string
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);
        $businessData = $context['business_data'] ?? [];

        $prompt = "حلل الطلب وحدد ماذا يريد العميل:\n\n";
        $prompt .= "رسالة العميل: \"{$userMessage}\"\n\n";
        $prompt .= "اذا سئل عن خدمه معينه جيب بيانات الخدمه هذه او سئل عن طبيب او يشتي يعرف الأطباء في عيادة معينه,\n";
        $prompt .= "او استفسار عن العيادات او حجز موعد,اي شيء انت فقط افهم سؤاله وجاوبه بالبيانات حق البزنز.\n";
        $prompt .= "أرجع فقط البيانات المطلوبه فقط بشكل منظم ومختصر :\n";
        $prompt .= "بيانات البزنس:\n" . json_encode($businessData, JSON_UNESCAPED_UNICODE) . "\n\n";
        $prompt .= "اذا لم تجد شيء فقط ارجع جملة مفيده انو العميل لم يوضح طلبه";

        $response = $aiService->chat($prompt, false, $this->analyzer);
        return $response['success'] ? trim($response['content']) : 'محادثة_عامة';
    }


    /**
     * 3. إنشاء الرد النهائي
     */
    private function generateFinalResponse(string $userMessage, string $searchResult, array $context): string
    {
        $aiService = AIServiceFactory::create($this->responder['provider']);

        $businessName = $context['business_data']['name'] ?? 'العيادة';

        $systemMessage = "أنت موظف في {$businessName}.\n";
        $systemMessage .= "هناك بيانات متوفره عن العيادة والاطباء والخدمات والمواعيد والاتصالات وغيرها من البيانات المهمه.\n";
        $systemMessage .= "البيانات المتاحة:\n{$searchResult}\n\n";
        $systemMessage .= "مهمتك الرد على استفسارات العملاء بأسلوب ودود، ومفيد.\n";
        $systemMessage .= "انت سعودي الجنسيه ومرح وتستخدم اللهجه السعوديه.\n";
        $systemMessage .= "- خلك لطيف، واضح، ولا تتجاوز 200 كلمة.\n";
        $systemMessage .= "- لا تكرر المعلومات،والتحيه خاصة لو قد حييت العميل بالرسائل السابقه, وركّز على إفادة العميل بناءً على التحليل.\n";
        $systemMessage .= "- إذا ما كان فيه بيانات متاحه، اعتذر بلُطف واقترح طريقة للمساعدة. لاتجيب بيانات من راسك لأنك ممثل مجمع طبي ضروري الصدق والأمانه\n\n";

        $response = $aiService->chat($userMessage, true, [
            'model' => $this->responder['model'],
            'temperature' => $this->responder['temperature'],
            'max_tokens' => $this->responder['max_tokens'],
            'conversation_history' => $context['conversation_history'] ?? [],
            'system_message' => $systemMessage
        ]);

        return $response['success'] ? $response['content'] : 'مرحبا عميلنا العزيز لقد تلقينا رسالتك وسيتم الرد بأقرب وقت ممكن';
    }
}
