<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Clinic extends Model
{

    protected $fillable = [
        'business_id',
        'name',
        'floor_number',
        'room_number',
        'phone_number',
        'opening_time',
        'closing_time',
        'is_open',
        'closing_reason',
    ];

    protected $casts = [
        'opening_time' => 'datetime',
        'closing_time' => 'datetime',
        'is_open' => 'boolean',
    ];

    public function business()
    {
        return $this->belongsTo(Business::class);
    }

    public function doctors()
    {
        return $this->hasMany(Doctor::class);
    }

    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    public function offers()
    {
        return $this->hasMany(ClinicOffer::class);
    }

    public function services()
    {
        return $this->hasMany(ClinicService::class);
    }
}
